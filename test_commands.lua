-- فایل تست برای بررسی عملکرد ریسورس
-- این فایل را در server.cfg اضافه نکنید، فقط برای تست است

-- تست دستورات ادمین برای بررسی عملکرد
if GetConvar('stg_debug', 'false') == 'true' then
    
    -- دستور تست برای بررسی دیتابیس
    RegisterCommand('stg_test_db', function(source, args, rawCommand)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer.getGroup() == 'admin' then
            MySQL.query("SELECT COUNT(*) as count FROM stg_clothing", {}, function(result)
                print("STG Clothing table has " .. result[1].count .. " records")
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"STG Test", "Database connection OK. Records: " .. result[1].count}
                })
            end)
        end
    end, false)

    -- دستور تست برای بررسی callback ها
    RegisterCommand('stg_test_callbacks', function(source, args, rawCommand)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer.getGroup() == 'admin' then
            ESX.TriggerServerCallback('stg_clothing:getWardrobe', function(data)
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"STG Test", "Wardrobe callback working. Items: " .. #data}
                })
            end)
        end
    end, false)

    -- دستور تست برای بررسی سیستم پول
    RegisterCommand('stg_test_money', function(source, args, rawCommand)
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer.getGroup() == 'admin' then
            ESX.TriggerServerCallback('stg_clothing:getMoney', function(result)
                TriggerClientEvent('chat:addMessage', source, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"STG Test", "Money system working. Result: " .. tostring(result)}
                })
            end, 'cash', 100)
        end
    end, false)

    print("^2[STG Clothing]^7 Debug mode enabled. Use /stg_test_db, /stg_test_callbacks, /stg_test_money")
end
