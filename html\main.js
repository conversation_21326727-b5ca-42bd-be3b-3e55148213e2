var lastContent = "mask"
var lastType = "clothe"

$(function() {
    window.addEventListener('message', function(event) {
        var data = event.data   
        if(data.type == "open" ) {
            $('body').show()
            $('.stg_clothing').show()
            selectContent('mask', 'face', 1)
            $('.content').removeClass('active')
            $('.'+data.shopType).addClass('active')
            lastType = data.shopType
        }
        if(data.type == "exit" ) {
            $('body').hide()
            $('.priceValue').text(formatBalance(0))
        }
        if(data.type == "updatePrice" ) {
            $('.priceValue').text(formatBalance(data.price))
        }
        if(data.type == "updateMax" ) {
            $('.colorMax').text(data.currentColor+"/ "+data.maxColor)
            $('.clotheMax').text(data.current+"/ "+data.max)

            $('.value_clothe').val(data.current)
            $('.value_color').val(data.currentColor)
        }
        if(data.type == "addWardrobe" ) {
            let div = `<div onclick="useWardrobe(`+data.id+`)" class="wardrobe">
            <img src="images/wardrobebg.png" class="wardrobebg">
            <p class="wardrobeTitle">`+data.name+`</p>
            <p class="buttonApply">Apply</p>
            <p onclick="deleteWardrobe(`+data.id+`)" class="buttonDelete">Delete</p>
            </div>`
            $('.wardrobeList').append(div)
        }
        if(data.type == "openWardrobe" ) {
            $('body').show()
            $('#wardrobeArea').show()
        }
    })
})

function deleteWardrobe(id) {
    $('.wardrobe').remove()
    $.post('http://stg_clothing/deleteWardrobe', JSON.stringify({
        id: id
    }));
}

function useWardrobe(id) {
    exit()
    $.post('http://stg_clothing/useWardrobe', JSON.stringify({
        id: id
    })); 
}

function selectContent(content, camera, id, prop, barber) {
    if(barber) {
        if(lastType != "barber") {
            return
        }
    }
    else {
        if(lastType != "clothe") {
            return
        }
    }
    $('.content_'+lastContent).attr('src','images/contentIcons/'+lastContent+'.png');
    $('.content_'+content).attr('src', 'images/contentIcons/'+content+'Active.png')
    lastContent = content

    $.post('http://stg_clothing/updateCamera', JSON.stringify({
        content: content,
        type: camera,
        id: id,
        prop: prop,
        barber: barber
    }));
}

function nextClothe() {
    $.post('http://stg_clothing/nextClothe', JSON.stringify({}));
}

function backClothe() {
    $.post('http://stg_clothing/backClothe', JSON.stringify({}));
}

function nextColor() {
    $.post('http://stg_clothing/nextColor', JSON.stringify({}));
}

function backColor() {
    $.post('http://stg_clothing/backColor', JSON.stringify({}));
}

function jobOutfits() {
    $.post('http://stg_clothing/jobOutfits', JSON.stringify({}));
}

function buy(type) {
    $.post('http://stg_clothing/buy', JSON.stringify({
        type: type
    }));
}

function changeClothe(value) {
    $.post('http://stg_clothing/changeClothe', JSON.stringify({
        value: $('.value_'+value).val()
    }));
}

function changeColor(value) {
    $.post('http://stg_clothing/changeColor', JSON.stringify({
        value: $('.value_'+value).val()
    }));
}

function rotate(status) {
    $.post('http://stg_clothing/rotate', JSON.stringify({
        status: status
    }));
}

function saveOutfit() {
    let name = $('#outfitInput').val()
    if(name.length > 0) {
        $.post('http://stg_clothing/saveOutfit', JSON.stringify({
            name: name
        }));
        $('#outfitInput').val("")
    }
}

window.addEventListener("keyup", (event) => {
    event.preventDefault();
    if (event.keyCode == 27) {
        exit()
    }
})

function exit() {
    $('.priceValue').text(formatBalance(0))

    $('body').hide()
    $('.stg_clothing').hide()
    $('#wardrobeArea').hide()

    $('.wardrobe').remove()

    $.post('http://stg_clothing/exit', JSON.stringify({}));
}

function formatBalance(balance) {
    var formatter = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      });
   return formatter.format(balance)   
}