STG = {}
STG.Locale = {}

STG.wardrobe = true
STG.wardrobeCommand = "wardrobe"

STG.barberPrice = 250

STG.blip = {
	["clothe"] = {
		label = "Clothing Shop",
		sprite = 73,
		scale = 0.7,
		color = 3
	},
	["barber"] = {
		label = "Barber Shop",
		sprite = 71,
		scale = 0.7,
		color = 1
	},

}

STG.Shops = {
	[1] = {
		type = "clothe",
		coord = vector3(72.3, -1399.1, 28.4),
	},
	[2] = {
		type = "clothe",
		coord = vector3(-703.8, -152.3, 36.4),
	},
	[3] = {
		type = "clothe",
		coord = vector3(-167.9, -299.0, 38.7),
	},
	[4] = {
		type = "clothe",
		coord = vector3(428.7, -800.1, 28.5),
	},
	[5] = {
		type = "clothe",
		coord = vector3(-829.4, -1073.7, 10.3),
	},
	[6] = {
		type = "clothe",
		coord = vector3(-1447.8, -242.5, 48.8),
	},
	[7] = {
		type = "clothe",
		coord = vector3(11.6, 6514.2, 30.9),
	},
	[8] = {
		type = "clothe",
		coord = vector3(123.6, -219.4, 53.6),
	},
	[9] = {
		type = "clothe",
		coord = vector3(1696.3, 4829.3, 41.1),
	},
	[10] = {
		type = "clothe",
		coord = vector3(618.1, 2759.6, 41.1),
	},
	[11] = {
		type = "clothe",
		coord = vector3(1190.6, 2713.4, 37.2),
	},
	[12] = {
		type = "clothe",
		coord = vector3(-1193.4, -772.3, 16.3),
	},
	[13] = {
		type = "clothe",
		coord = vector3(-3172.5, 1048.1, 19.9),
	},
	[14] = {
		type = "clothe",
		coord = vector3(-1108.4, 2708.9, 18.1)
	},
	-- Barbers
	[15] = {
		type = "barber",
		coord = vector3(-814.3, -183.8, 36.6),
	},
	[16] = {
		type = "barber",
		coord = vector3(136.8, -1708.4, 28.3),
	},
	[17] = {
		type = "barber",
		coord = vector3(-1282.6, -1116.8, 6.0),
	},
	[18] = {
		type = "barber",
		coord = vector3(1931.5, 3729.7, 31.8),
	},
	[19] = {
		type = "barber",
		coord = vector3(1212.8, -472.9, 65.2),
	},
	[20] = {
		type = "barber",
		coord = vector3(-32.9, -152.3, 56.1),
	},
	[21] = {
		type = "barber",
		coord = vector3(-278.1, 6228.5, 30.7)
	},
}

STG.prices = {
	[1] = {
		componentID = 1, -- Mask
		price = 400,
		isProp = false
	},
	[2] = {
		componentID = 0, -- Hat
		price = 100,
		isProp = true
	},
	[3] = {
		componentID = 7, -- Jeverly
		price = 100,
		isProp = false
	},
	[4] = {
		componentID = 1, -- Glasses
		price = 100,
		isProp = true
	},
	[5] = {
		componentID = 5, -- Bag
		price = 100,
		isProp = false
	},
	[6] = {
		componentID = 8, -- T-shirt
		price = 100,
		isProp = false
	},
	[7] = {
		componentID = 6, -- Watches
		price = 100,
		isProp = true
	},
	[8] = {
		componentID = 3, -- Gloves
		price = 100,
		isProp = false
	},
	[9] = {
		componentID = 4, -- Pants
		price = 100,
		isProp = false
	},
	[10] = {
		componentID = 6, -- Shoes
		price = 100,
		isProp = false
	},
	[11] = {
		componentID = 11, -- Torso
		price = 100,
		isProp = false
	},
	[12] = {
		componentID = 9, -- Armor
		price = 100,
		isProp = false
	},
}