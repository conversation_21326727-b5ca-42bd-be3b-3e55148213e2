# STG Clothing Script - Fixed Version

این ریسورس یک سیستم کامل لباس و آرایشگاه برای سرورهای ESX FiveM است که تمام باگ‌های آن فیکس شده است.

## تغییرات انجام شده:

### 1. فیکس MySQL
- تبدیل از `mysql-async` به `oxmysql` 
- تبدیل تمام query ها به فرمت جدید oxmysql
- حذف استفاده از `MySQL.Sync` و `MySQL.Async`

### 2. فیکس Callback ها
- تغییر نام callback تکراری `stg_clothing:getSkin` به `stg_clothing:getOutfit`
- فیکس نام callback `pntg_clothing:getMoney` به `stg_clothing:getMoney`

### 3. فیکس وابستگی‌ها
- حذف وابستگی به `skinchanger`
- پیاده‌سازی سیستم اسکین مستقل
- اضافه کردن تابع `loadSkinFromData` برای بارگذاری لباس‌های ذخیره شده

### 4. بهبود کد
- اضافه کردن null check ها
- بهبود error handling
- استفاده از default values

## نیازمندی‌ها:

- ESX Framework
- oxmysql (به جای mysql-async)

## نصب:

1. فایل‌های SQL را در دیتابیس اجرا کنید:
   ```sql
   -- اجرای sql.sql برای جدول stg_clothing
   -- اجرای sq1.sql برای جدول playerskins
   ```

2. ریسورس را در پوشه resources قرار دهید

3. در server.cfg اضافه کنید:
   ```
   ensure stg_clothing
   ```

## ویژگی‌ها:

- سیستم خرید لباس با پول نقد یا بانک
- سیستم کمد لباس (wardrobe) با دستور `/wardrobe`
- آرایشگاه برای تغییر مو و ریش
- ذخیره و بارگذاری لباس‌های شخصی
- سیستم قیمت‌گذاری قابل تنظیم
- رابط کاربری HTML/CSS/JS

## تنظیمات:

تمام تنظیمات در فایل `config.lua` قابل تغییر است:
- مکان‌های فروشگاه‌ها
- قیمت‌ها
- تنظیمات blip ها
- فعال/غیرفعال کردن کمد لباس

## دستورات:

- `/wardrobe` - باز کردن کمد لباس (اگر فعال باشد)

## مشکلات رفع شده:

✅ خطای MySQL deprecated functions
✅ تداخل نام callback ها  
✅ وابستگی غیرضروری به skinchanger
✅ مشکل در بارگذاری لباس‌های ذخیره شده
✅ خطاهای null reference
✅ مشکل در سیستم پرداخت

## Troubleshooting:

### خطای `attempt to call a nil value (field 'getAccount')`:

این خطا معمولاً به دلیل تفاوت در نسخه‌های ESX رخ می‌دهد. برای حل:

1. **تست ESX functions**:
   ```
   /stg_debug
   ```

2. **تست سیستم پرداخت**:
   ```
   /stg_test_payment 100 cash
   /stg_test_payment 100 bank
   ```

3. **بررسی نسخه ESX**:
   - ESX Legacy: از `xPlayer.getMoney()` استفاده می‌کند
   - ESX 1.2: از `xPlayer.money` استفاده می‌کند
   - ESX Final: ترکیبی از هر دو

### مشکلات رایج:

- **MySQL Error**: مطمئن شوید oxmysql نصب است
- **ESX Error**: مطمئن شوید ESX به درستی لود شده
- **Account Error**: بررسی کنید که جدول accounts وجود دارد

### حذف فایل‌های Debug:

بعد از حل مشکل، این خطوط را از fxmanifest.lua حذف کنید:
```lua
"debug.lua", -- فقط برای تست - بعداً حذف کنید
```

## پشتیبانی:

اگر مشکلی داشتید، مطمئن شوید که:
- oxmysql نصب و فعال است
- جداول دیتابیس درست ایجاد شده‌اند
- ESX Framework به درستی کار می‌کند
- نسخه ESX شما پشتیبانی می‌شود

---

**نکته مهم:** این ورژن فیکس شده مستقل از skinchanger کار می‌کند و تمام عملکردهای لازم را داخلی پیاده‌سازی کرده است.
