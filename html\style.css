@font-face {
    font-family: OxaniumBold;
    src: url(fonts/Oxanium-Bold.ttf);
}

@font-face {
    font-family: OxaniumSemiBold;
    src: url(fonts/Oxanium-SemiBold.ttf);
}

@font-face {
    font-family: OxaniumLight;
    src: url(fonts/Oxanium-Light.ttf);
}

body{
    overflow: hidden;
    display: none;
}

.title{
    width: 8.5vw;
    position: absolute;
    font-family: OxaniumBold;
    font-size: 1.68vw;
    left: 0.8vw;
    top: -0.1vw;


    color:#FF123D;
    text-align: center;
}

.title1{
    width: 8.5vw;
    position: absolute;
    color: white;
    font-family: OxaniumBold;
    font-size: 1.68vw;
    top: 0.6vw;
    left:1vw;
    text-align: center;
}

.description{
    position: absolute;
    opacity: 0.25;
    color: white;
    font-family: OxaniumLight;
    font-size: 0.85vw;
    left: 0.8vw;
    top: 3.5vw;
    width: 14vw;
}

.clothes{
    position: absolute;
    top: 8.5vw;
    left: 0.8vw;
    width: 14vw;
    height: 33vw;
}

.content{
    position: relative;
    top: -1.05vw;
    left: -0.5vw;
    width: 4.6vw;
    height: 4.6vw;
    float: left;
    margin-left:0.8vw;
    margin-top:1.05vw;
    transition: 0.3s;
    opacity: 0.3;
}

.content:hover{
    transform: scale(1.1);
}

.contentImage{
    position: absolute;
    width: 4.7vw;
    height: 4.7vw;
}

.details{
    position: absolute;
    width: 100%;
    height: 100%;
    top:0;
    left:0;
    z-index:-5
}

.stars{
    position: absolute;
    width: 26vw;
    height: 55.5vw;
    top:0;
    left:0
}

.contentTitle{
    position: absolute;
    top: -0.60vw;
    right: 0.3vw;
    font-size: 0.9vw;
    color:white;
    font-family: OxaniumBold;
}

.variationArea{
    position: absolute;
    top: 35.2vw;
    left: 1.2vw;
    font-size: 0.75vw;
    font-family: OxaniumLight;
    color: white;
}

.colorArea{
    position: absolute;
    top: 38.5vw;
    left: 1.2vw;
    font-size: 0.75vw;
    font-family: OxaniumLight;
    color: white;
}

.variantionText{
    position: absolute;
    width: 10vw;
    top: -0.52vw;
    left: 0.7vw;
    font-size: 0.75vw;
    font-family: OxaniumLight;
    color: white;
}

.variationMain{
    position: absolute;
    width: 10.5vw;
    top: -0.2vw;
    height: 3vw;
    left: -1.2vw;
}

.variationLeft{
    width: 1.2vw;
    height: 1.2vw;
    position: absolute;
    top: 1.52vw;
    left: -0.4vw;

}

.variationRight{
    width: 1.2vw;
    height: 1.2vw;
    position: absolute;
    top: 1.52vw;
    left: 7.7vw;
}

.variationValue{
    width: 7vw;
    height: 0.8vw;
    position: absolute;
    color:white;
    font-family: OxaniumLight;
    font-size: 0.6vw;
    top: 1.1vw;
    left: 0.6vw;
    text-align: center;
}

.price{
    width: 5vw;
    height: 0.8vw;
    position: absolute;
    color:white;
    font-family: OxaniumLight;
    font-size: 0.7vw;
    top: 43vw;
    left: 0.5vw;
}

.priceValue{
    position: absolute;
    width: 7.4vw;
    height: 2.2vw;
    font-size: 2vw;
    left: -0.45vw;
    top: 0.9vw;
    font-family: OxaniumSemiBold;
    color: #FF275B;
    text-align: center;
}

.priceCard{
    position: absolute;
    width: 2vw;
    height: 2vw;
    left: 7.4vw;
    top: -0.55vw;
}

.priceCash{
    position: absolute;
    width: 2vw;
    height: 2vw;
    left: 7.4vw;
    top: 1.8vw;
}

.saveOutfit{
    position: absolute;
    top: 43.5vw;
    width: 11vw;
    height: 3vw;
    left: 43.5vw;
}

.saveTitle{
    position: absolute;
    width: 7.4vw;
    height: 2.2vw;
    font-size: 0.7vw;
    left: 1.7vw;
    top: -0.2vw;
    font-family: OxaniumLight;
    color: white;
}

.saveMain{
    position: absolute;
    width: 11vw;
    height: 3.1vw;
    left: 0vw;
    top: 0vw;
}

.saveName{
    position: absolute;
    width: 5vw;
    height: 1.3vw;
    left: 3.8vw;
    top: 1.15vw;
    opacity: 0.3;
    color: white;
}

.saveConfirm{
    position: absolute;
    width: 1vw;
    height: 1vw;
    left: 9.3vw;
    top: 1.8vw;
}

.indicator{
    position: absolute;

    width: 9vw;
    height: 12vw;
    top: 34vw;
    left: 88vw;
}

.lanetolasiakpartisimgesi{
    position: absolute;
    width: 13vw;
    height: 13vw;
    top: -3vw;
    left: -1.3vw;
}

.rightMouse{
    position: absolute;
    width: 1.5vw;
    height: 1.8vw;
    top: 7.5vw;
    left: -0.1vw;
}

.leftMouse{
    position: absolute;
    width: 1.5vw;
    height: 1.8vw;
    top: 10.2vw;
    left: -0.1vw;
}

.rotateRight{
    position: absolute;
    width: 6.1vw;
    height: 1.3vw;
    top: 6.5vw;
    left: 2.2vw;
    color: white;
    font-size: 0.9vw;
    font-family: OxaniumSemiBold;
}

.rotateDescription{
    position: absolute;
    font-size: 0.7vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.37);
    top: 7.9vw;
    left: 2.2vw;
}

.rotateLeft{
    position: absolute;
    width: 6.1vw;
    height: 1.3vw;
    top: 9.4vw;
    left: 2.2vw;
    color: white;
    font-size: 0.9vw;
    font-family: OxaniumSemiBold;
}

.rotateLeftDescription{
    position: absolute;
    font-size: 0.7vw;
    font-family: OxaniumLight;
    color: rgba(255, 255, 255, 0.37);
    top: 10.75vw;
    left: 2.2vw;
}

#outfitInput{
    position: absolute;
    top: 1.6vw;
    left: 1.6vw;
    text-align: center;
    font-size: 0.7vw;
    width: 7.5vw;
    height: 1.5vw;
    background-color: rgba(255, 255, 255, 0);
    border: none;
    outline: none;
    z-index: 999;
    color: #fff
}

#wardrobeArea{
    position: absolute;
    width: 30vw;
    height: 26vw;

    top:15vw;
    left:35vw;
    display: none;
}

.wardrobeImage{
    position: absolute;
    width: 28vw;
    height: 23.4vw;

    top:1.4vw;
    left:0.9vw;
}

.wardrobeText{
    position: absolute;
    width: 28vw;

    top:0.5vw;
    left:0.9vw;
    text-align: center;

    font-family: 'OxaniumBold';
    font-style: normal;
    font-weight: 700;
    font-size: 1.65vw;

    color: #FF275B;

    text-shadow: 0px 4px 30px rgba(255, 39, 91, 0.75);
}

.wardrobebg{
    position: absolute;
    width: 7.3vw;
    height: 7.3vw;

    left:2.6vw;
    top:4.7vw;
}

.wardrobeTitle{
    position: absolute;
    width: 7.25vw;
    left:2.7vw;
    top:3.8vw;
    font-family: 'OxaniumBold';

    background: linear-gradient(91.88deg, #FFFFFF 0%, #FFFFFF 0.01%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;

    text-shadow: 0px 4px 30px rgba(255, 255, 255, 0.5);
    text-align: center;
    font-size: 1.1vw;
}

.buttonApply{
    position: absolute;
    width: 3.05vw;
    height: 1.28vw;
    left:3vw;
    top:11.7vw;
    font-family: 'OxaniumBold';

    color:white;
    text-align: center;
    font-size: 0.7vw;
    line-height: 1.4vw;
    background: rgba(15, 225, 10, 0.5);
    border: 0.1vw solid #0FE10A;
}


.buttonDelete{
    position: absolute;
    width: 3.05vw;
    height: 1.28vw;
    left:6.4vw;
    top:11.7vw;
    font-family: 'OxaniumBold';

    color:white;
    text-align: center;
    font-size: 0.7vw;
    line-height: 1.4vw;
    background: rgba(225, 10, 10, 0.5);
    border: 0.1vw solid #E10A0A;
}

.wardrobe{
    position: relative;
    width: 7.3vw;
    height: 7.3vw;

    left:0vw;
    top:-7vw;
    float: left;
    margin:2.4vw 0vw 0vw 0.6vw;
    transition: 0.3s;
}

.wardrobe:hover{
    transform: scale(0.95);
}

.wardrobeList{
    position: absolute;
    width: 30vw;
    height: 19vw;

    top:5vw;
    left:0vw;

    overflow-y: scroll;
}

.wardrobeList::-webkit-scrollbar {
    width: 0vw;
}

img {
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    user-drag: none;
}

*:not(input):not(textarea) {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
    -ms-user-select: none;
    user-select: none
}

#rotateCheck{
    position:absolute;
    width:100%;
    height:100%;
}

#barberArea{
    position:absolute;
    width:1vw;
    left:10.8vw;
}

.active{
    opacity: 1;
}