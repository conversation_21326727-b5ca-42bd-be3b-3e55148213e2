<html>

<head>
    <title>stg_clothing - by GENER4L</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style.css" type="text/css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js" integrity="sha256-/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=" crossorigin="anonymous"></script>
    <!-- <script src="nui://game/ui/jquery.js" type="text/javascript"></script> -->
    <script src="main.js"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com%22%3E/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;800&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css" integrity="sha512-KfkfwYDsLkIlwQp6LFnl8zNdLGxu9YAA1QvwINks4PhcElQSvqcyVLLD9aMhXd13uQjoXtEKNosOWaZqXgel0g==" crossorigin="anonymous" referrerpolicy="no-referrer"/>
</head>

<body>
    <div oncontextmenu="rotate('right')" onclick="rotate('left')" id="rotateCheck"></div>
    <div class="stg_clothing">
        <img class="details" src="images/details.png">
        <img class="stars" src="images/stars.png">
        <p class="title">CLOTHING</p>
        <p class="title1">STORE</p>
        <p class="description">Welcome to our store here you can find quality and brand clothing.</p>
        <div class="clothes">
            <div onclick="selectContent('hat', 'face', 0, true)" class="content clothe">
                <img class="contentImage" src="images/contents/hat.png">
                <p class="contentTitle">Hat</p>
            </div>
            <div onclick="selectContent('mask', 'face', 1)" class="content clothe">
                <img class="contentImage" src="images/contents/mask.png">
                <p class="contentTitle">Mask</p>
            </div>
            <div onclick="selectContent('jeverly', 'face', 7)" class="content clothe">
                <img class="contentImage" src="images/contents/jewelry.png">
                <p class="contentTitle">Jewelry</p>
            </div>
            <div onclick="selectContent('glass', 'face', 1, true)" class="content clothe">
                <img class="contentImage" src="images/contents/glasses.png">
                <p class="contentTitle">Glasses</p>
            </div>
            <div onclick="selectContent('backpack', 'top', 5)" class="content clothe">
                <img class="contentImage" src="images/contents/backpacks.png">
                <p class="contentTitle">Backpacks</p>
            </div>
            <div onclick="selectContent('tshirt', 'top', 8)" class="content clothe">
                <img class="contentImage" src="images/contents/tshirts.png">
                <p class="contentTitle">T-Shirts</p>
            </div>
            <div onclick="selectContent('watch', 'top', 6, true)" class="content clothe">
                <img class="contentImage" src="images/contents/watches.png">
                <p class="contentTitle">Watches</p>
            </div>
            <div onclick="selectContent('gloves', 'top', 3)" class="content clothe">
                <img class="contentImage" src="images/contents/gloves.png">
                <p class="contentTitle">Gloves</p>
            </div>
            <div onclick="selectContent('pants', 'pants', 4)"  class="content clothe">
                <img class="contentImage" src="images/contents/pants.png">
                <p class="contentTitle">Pants</p>
            </div>
            <div onclick="selectContent('shoes', 'shoes', 6)" class="content clothe">
                <img class="contentImage" src="images/contents/shoes.png">
                <p class="contentTitle">Shoes</p>
            </div>
            <div onclick="selectContent('torso', 'top', 11)" class="content clothe">
                <img class="contentImage" src="images/contents/torso.png">
                <p class="contentTitle">Torso</p>
            </div>
            <div onclick="selectContent('armor', 'top', 9)" class="content clothe">
                <img class="contentImage" src="images/contents/vest.png">
                <p class="contentTitle">Armor</p>
            </div>
            <!-- barber -->
            <div id="barberArea">
                <div onclick="selectContent('hair', 'face', 2, false, true)"  class="content barber">
                    <img class="contentImage" src="images/contents/hair.png">
                    <p class="contentTitle">Hair</p>
                </div>
                <div onclick="selectContent('shoes', 'face', 1, false, true)" class="content barber">
                    <img class="contentImage" src="images/contents/beard.png">
                    <p class="contentTitle">Beard</p>
                </div>
                <div onclick="selectContent('torso', 'face', 4, false, true)" class="content barber">
                    <img class="contentImage" src="images/contents/makeup.png">
                    <p class="contentTitle">Make up</p>
                </div>
                <!-- <div onclick="selectContent('armor', 'top', 9)" class="content">
                    <img class="contentImage" src="images/contents/totto.png">
                    <p class="contentTitle">Totto</p>
                </div> -->
            </div>
            <div class="variationArea">
                <p class="variantionText">Clothing Variation</p>
                <img class="variationMain" src="images/valueBox.png">
                <img onclick="backClothe()" class="variationLeft" src="images/left.png">
                <img onclick="nextClothe()" class="variationRight" src="images/right.png">
                <p class="variationValue clotheMax">333 / 134</p>
            </div>
            <div class="colorArea">
                <p class="variantionText">Color Variation</p>
                <img class="variationMain" src="images/valueBox.png">
                <img onclick="backColor()" class="variationLeft" src="images/left.png">
                <img onclick="nextColor()" class="variationRight" src="images/right.png">
                <p class="variationValue colorMax">333 / 134</p>
            </div>
            <div class="price">TOTAL PRICE 
                <div class="priceValue">$0.00</div>
                <img onclick="buy('bank')" class=priceCard src="images/card.png">
                <img onclick="buy('cash')" class=priceCash src="images/cash.png">
            </div>
            <div class="saveOutfit">
                <img class="saveMain" src="images/valueBox.png">
                <p class="saveTitle">Save Outfit</p>
                <input type="text" id="outfitInput" placeholder="Outfit name...">
                <img onclick="saveOutfit()" class="saveConfirm" src="images/tick.png">
            </div>

            <div class="indicator">
                <img class="lanetolasiakpartisimgesi"src="images/lanetolasiakpartisimgesi.png">
                <img class="rightMouse" src="images/rightmouse.png">
                <img class="leftMouse" src="images/leftmouse.png">
                <p class="rotateRight">Rotate Right</p>
                <p class="rotateDescription">Right mouse button</p>
                <p class="rotateLeft">Rotate Left</p>
                <p class="rotateLeftDescription">Right mouse button</p>
            </div>
        </div>
    </div>
    <div id="wardrobeArea">
        <img class="wardrobeImage" src="images/wardrobe.png">
        <p class="wardrobeText">WARDROBE</p>
        <div class="wardrobeList"></div>
    </div>
</body>