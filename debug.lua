-- فایل debug برای تشخیص مشکلات ESX
-- این فایل را فقط برای تست استفاده کنید

-- تست ESX و player functions
RegisterCommand('stg_debug', function(source, args, rawCommand)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then
        print("^1[STG Debug] Player not found!")
        return
    end
    
    print("^2[STG Debug] Player found: " .. xPlayer.identifier)
    
    -- تست money functions
    print("^3[STG Debug] Testing money functions...")
    
    if xPlayer.getMoney then
        print("^2[STG Debug] getMoney() available: " .. xPlayer.getMoney())
    else
        print("^1[STG Debug] getMoney() not available")
    end
    
    if xPlayer.money then
        print("^2[STG Debug] money property available: " .. xPlayer.money)
    else
        print("^1[STG Debug] money property not available")
    end
    
    if xPlayer.removeMoney then
        print("^2[STG Debug] removeMoney() available")
    else
        print("^1[STG Debug] removeMoney() not available")
    end
    
    -- تست account functions
    print("^3[STG Debug] Testing account functions...")
    
    if xPlayer.getAccount then
        print("^2[STG Debug] getAccount() available")
        local bankAccount = xPlayer.getAccount('bank')
        if bankAccount then
            print("^2[STG Debug] Bank account found: " .. bankAccount.money)
        else
            print("^1[STG Debug] Bank account not found")
        end
    else
        print("^1[STG Debug] getAccount() not available")
    end
    
    if xPlayer.removeAccountMoney then
        print("^2[STG Debug] removeAccountMoney() available")
    else
        print("^1[STG Debug] removeAccountMoney() not available")
    end
    
    -- تست ESX version
    if ESX.GetConfig then
        print("^2[STG Debug] ESX Config available")
    else
        print("^1[STG Debug] ESX Config not available")
    end
    
    TriggerClientEvent('chat:addMessage', source, {
        color = {0, 255, 0},
        multiline = true,
        args = {"STG Debug", "Check console for debug info"}
    })
end, false)

-- تست ساده پول
RegisterCommand('stg_test_payment', function(source, args, rawCommand)
    local amount = tonumber(args[1]) or 100
    local paymentType = args[2] or "cash"
    
    ESX.TriggerServerCallback('stg_clothing:getMoney', function(success)
        TriggerClientEvent('chat:addMessage', source, {
            color = success and {0, 255, 0} or {255, 0, 0},
            multiline = true,
            args = {"STG Test", "Payment test: " .. (success and "SUCCESS" or "FAILED")}
        })
    end, paymentType, amount)
end, false)

print("^2[STG Clothing Debug]^7 Commands loaded: /stg_debug, /stg_test_payment [amount] [cash/bank]")
